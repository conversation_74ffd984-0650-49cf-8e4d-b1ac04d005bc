<template>
  <view class="notification-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 现代化通知头部 -->
    <view class="modern-notification-header">
      <view class="header-background">
        <view class="header-gradient"></view>
        <view class="header-pattern"></view>
      </view>
      <view class="header-content">
        <view class="header-left">
          <view class="back-button-modern" @click="goBack">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
          </view>
          <view class="page-info">
            <text class="page-title-modern">通知中心</text>
            <text class="page-subtitle-modern">消息提醒 • 系统通知</text>
          </view>
        </view>
        <view class="header-right">
          <view class="notification-stats">
            <text class="stats-count">{{ filteredNotifications.length }}</text>
            <text class="stats-label">条消息</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 现代化通知控制栏 -->
    <view class="notification-controls">
      <view class="controls-background">
        <view class="controls-gradient"></view>
      </view>
      <view class="controls-content">
        <view class="filter-section">
          <view class="filter-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
          </view>
          <picker mode="selector" :value="selectedPeriodIndex" :range="periodOptions" @change="onPeriodChange">
            <view class="period-selector-modern">
              <text class="selector-text">{{ periodOptions[selectedPeriodIndex] }}</text>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </view>
          </picker>
        </view>
        <view class="action-section">
          <view class="clear-button-modern" @click="clearAllNotifications">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
            </svg>
            <text class="button-text">清空</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 通知列表 -->
    <scroll-view class="notification-list" scroll-y>
      <view v-if="filteredNotifications.length === 0" class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <text class="empty-text">暂无通知</text>
        <text class="empty-desc">当有新的系统消息时，会在这里显示</text>
      </view>

      <view v-for="(notification, index) in filteredNotifications" :key="notification.id" 
            :class="['notification-item', notification.type]"
            :style="{ 'animation-delay': (index * 0.1) + 's' }">
        <view class="notification-header">
          <view class="notification-icon">
            <svg v-if="notification.type === 'normal'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <svg v-else-if="notification.type === 'warning'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
          </view>
          <view class="notification-meta">
            <text class="notification-title">{{ notification.title }}</text>
            <text class="notification-time">{{ formatTime(notification.timestamp) }}</text>
          </view>
          <view class="notification-actions">
            <view class="action-button" @click="markAsRead(notification.id)">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
            </view>
            <view class="action-button delete" @click="deleteNotification(notification.id)">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"></path>
              </svg>
            </view>
          </view>
        </view>
        <text class="notification-content">{{ notification.content }}</text>
        <view v-if="notification.data" class="notification-data">
          <text class="data-label">相关数据:</text>
          <text class="data-value">{{ formatNotificationData(notification.data) }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRadiationStore } from '@/stores/radiation'
import { useToastManager } from '@/utils/toastManager'

export default {
  name: 'NotificationPage',
  setup() {
    const radiationStore = useRadiationStore()
    const toastManager = useToastManager()
    
    const selectedPeriodIndex = ref(0)
    const periodOptions = ['今天', '最近3天', '最近一周', '最近一月', '全部']
    
    // 模拟通知数据
    const notifications = ref([
      {
        id: 1,
        type: 'warning',
        title: '辐射剂量率异常',
        content: '检测到辐射剂量率超过安全阈值，请注意防护',
        timestamp: Date.now() - 1000 * 60 * 30, // 30分钟前
        data: { doseRate: 1.2, threshold: 1.0 },
        read: false
      },
      {
        id: 2,
        type: 'normal',
        title: '设备连接成功',
        content: 'MQTT服务器连接已建立，数据传输正常',
        timestamp: Date.now() - 1000 * 60 * 60 * 2, // 2小时前
        read: true
      },
      {
        id: 3,
        type: 'error',
        title: '设备电量不足',
        content: '检测设备电量低于20%，请及时充电',
        timestamp: Date.now() - 1000 * 60 * 60 * 6, // 6小时前
        data: { batteryLevel: 15 },
        read: false
      }
    ])

    const filteredNotifications = computed(() => {
      const now = Date.now()
      const periods = {
        0: 24 * 60 * 60 * 1000, // 今天
        1: 3 * 24 * 60 * 60 * 1000, // 最近3天
        2: 7 * 24 * 60 * 60 * 1000, // 最近一周
        3: 30 * 24 * 60 * 60 * 1000, // 最近一月
        4: Infinity // 全部
      }
      
      const period = periods[selectedPeriodIndex.value]
      return notifications.value.filter(n => now - n.timestamp <= period)
    })

    const onPeriodChange = (e) => {
      selectedPeriodIndex.value = e.detail.value
    }

    const clearAllNotifications = () => {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有通知吗？',
        success: (res) => {
          if (res.confirm) {
            notifications.value = []
            toastManager.success('已清空所有通知')
          }
        }
      })
    }

    const markAsRead = (id) => {
      const notification = notifications.value.find(n => n.id === id)
      if (notification) {
        notification.read = true
        toastManager.success('已标记为已读')
      }
    }

    const deleteNotification = (id) => {
      const index = notifications.value.findIndex(n => n.id === id)
      if (index !== -1) {
        notifications.value.splice(index, 1)
        toastManager.success('通知已删除')
      }
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60 * 1000) {
        return '刚刚'
      } else if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`
      } else if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }

    const formatNotificationData = (data) => {
      if (data.doseRate) {
        return `剂量率: ${data.doseRate} μSv/h (阈值: ${data.threshold} μSv/h)`
      }
      if (data.batteryLevel) {
        return `电量: ${data.batteryLevel}%`
      }
      return JSON.stringify(data)
    }

    const goBack = () => {
      uni.navigateBack()
    }

    onMounted(() => {
      // 可以在这里加载真实的通知数据
    })

    return {
      selectedPeriodIndex,
      periodOptions,
      notifications,
      filteredNotifications,
      onPeriodChange,
      clearAllNotifications,
      markAsRead,
      deleteNotification,
      formatTime,
      formatNotificationData,
      goBack
    }
  }
}
</script>

<style scoped>
/* 通知页面容器 */
.notification-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  padding-bottom: 100px;
}

/* 现代化通知头部样式 */
.modern-notification-header {
  position: relative;
  padding: 20px;
  margin-bottom: 20px;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.header-pattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle at center,
    rgba(59, 130, 246, 0.1) 0%,
    transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.back-button-modern {
  width: 40px;
  height: 40px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button-modern:hover {
  background: #3b82f6;
  transform: scale(1.05);
}

.back-button-modern svg {
  width: 20px;
  height: 20px;
  color: #64748b;
  transition: all 0.3s ease;
}

.back-button-modern:hover svg {
  color: #ffffff;
}

.page-info {
  flex: 1;
}

.page-title-modern {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.3;
}

.page-subtitle-modern {
  display: block;
  font-size: 14px;
  color: #64748b;
  margin-top: 2px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.notification-stats {
  text-align: right;
}

.stats-count {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
}

.stats-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

/* 现代化通知控制栏 */
.notification-controls {
  position: relative;
  margin: 0 20px 20px;
  overflow: hidden;
}

.controls-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.controls-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.controls-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.filter-icon svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.period-selector-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-selector-modern:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.selector-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.period-selector-modern svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.action-section {
  display: flex;
  align-items: center;
}

.clear-button-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.clear-button-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.clear-button-modern svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.button-text {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}

/* 顶部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  padding: 32rpx 40rpx 20rpx;
  align-items: center;
  position: relative;
  z-index: 10;
  background-color: #f0f0f0;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 44rpx;
  font-weight: 700;
  color: #222;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.back-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #222;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: scale(1.05);
}

.back-button svg {
  width: 40rpx;
  height: 40rpx;
  color: white;
}

/* 通知设置栏 */
.settings-bar {
  padding: 24rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.period-selector {
  display: flex;
  align-items: center;
}

.period-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.period-dropdown {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  font-size: 28rpx;
  color: #333;
}

.clear-button {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  background-color: #f44336;
  color: white;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-button:hover {
  background-color: #e53935;
}

/* 通知列表 */
.notification-list {
  flex: 1;
  padding: 32rpx 40rpx;
  background-color: #f7f7f7;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-state svg {
  width: 120rpx;
  height: 120rpx;
  color: #ccc;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  line-height: 1.5;
}

.notification-item {
  background-color: white;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  animation: slideIn 0.3s ease-out forwards;
}

.notification-item.normal {
  border-left: 8rpx solid #3498db;
}

.notification-item.warning {
  border-left: 8rpx solid #e67e22;
}

.notification-item.error {
  border-left: 8rpx solid #e74c3c;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.notification-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.notification-icon svg {
  width: 48rpx;
  height: 48rpx;
}

.notification-item.normal .notification-icon svg {
  color: #3498db;
}

.notification-item.warning .notification-icon svg {
  color: #e67e22;
}

.notification-item.error .notification-icon svg {
  color: #e74c3c;
}

.notification-meta {
  flex: 1;
}

.notification-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 8rpx;
  display: block;
}

.notification-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.notification-actions {
  display: flex;
  gap: 16rpx;
}

.action-button {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button svg {
  width: 24rpx;
  height: 24rpx;
  color: #666;
}

.action-button:hover {
  background-color: #e0e0e0;
}

.action-button.delete:hover {
  background-color: #ffebee;
}

.action-button.delete:hover svg {
  color: #f44336;
}

.notification-content {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: block;
}

.notification-data {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx;
  margin-top: 16rpx;
}

.data-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.data-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: block;
}
</style>
