/**
 * 高级机器学习预测算法
 * 基于时间序列分析、线性回归和周期性模式识别的复合预测模型
 */

export class RadiationPredictionModel {
  constructor() {
    this.historicalData = []
    this.modelParameters = {
      trendWeight: 0.4,      // 趋势权重
      seasonalWeight: 0.3,   // 季节性权重
      randomWeight: 0.3,     // 随机性权重
      errorMargin: 0.05,     // 5%误差带
      smoothingFactor: 0.3,  // 指数平滑因子
      predictionDays: 7      // 预测天数
    }
    this.modelState = {
      isInitialized: false,
      lastUpdate: null,
      trendSlope: 0,
      seasonalPatterns: {},
      volatility: 0
    }
  }

  /**
   * 初始化模型并训练
   * @param {Array} historicalData - 历史数据数组
   */
  initializeModel(historicalData) {
    if (!historicalData || historicalData.length < 10) {
      throw new Error('需要至少10个历史数据点来初始化预测模型')
    }

    this.historicalData = this.preprocessData(historicalData)
    this.trainModel()
    this.modelState.isInitialized = true
    this.modelState.lastUpdate = new Date()
    
    console.log('预测模型初始化完成，数据点数量:', this.historicalData.length)
  }

  /**
   * 数据预处理（优化版 - 减少数据点数）
   * @param {Array} rawData - 原始数据
   * @returns {Array} 处理后的数据
   */
  preprocessData(rawData) {
    // 数据清洗和标准化
    const cleanedData = rawData
      .filter(item => item && typeof item.doseRate === 'number' && !isNaN(item.doseRate))
      .map(item => ({
        timestamp: new Date(item.timestamp),
        doseRate: item.doseRate,
        cps: item.cps || 0
      }))
      .sort((a, b) => a.timestamp - b.timestamp)

    // 数据抽样 - 每2个数据点取1个，减少一半数据量
    const sampledData = cleanedData.filter((_, index) => index % 2 === 0)

    // 异常值检测和处理
    return this.removeOutliers(sampledData)
  }

  /**
   * 异常值检测和移除
   * @param {Array} data - 数据数组
   * @returns {Array} 处理后的数据
   */
  removeOutliers(data) {
    if (data.length < 4) return data

    const values = data.map(item => item.doseRate)
    const q1 = this.quantile(values, 0.25)
    const q3 = this.quantile(values, 0.75)
    const iqr = q3 - q1
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    return data.filter(item => 
      item.doseRate >= lowerBound && item.doseRate <= upperBound
    )
  }

  /**
   * 计算分位数
   * @param {Array} arr - 数值数组
   * @param {number} q - 分位数 (0-1)
   * @returns {number} 分位数值
   */
  quantile(arr, q) {
    const sorted = [...arr].sort((a, b) => a - b)
    const pos = (sorted.length - 1) * q
    const base = Math.floor(pos)
    const rest = pos - base
    
    if (sorted[base + 1] !== undefined) {
      return sorted[base] + rest * (sorted[base + 1] - sorted[base])
    } else {
      return sorted[base]
    }
  }

  /**
   * 训练预测模型
   */
  trainModel() {
    this.calculateTrend()
    this.extractSeasonalPatterns()
    this.calculateVolatility()
  }

  /**
   * 计算趋势 - 使用加权线性回归
   */
  calculateTrend() {
    const data = this.historicalData
    const n = data.length
    
    if (n < 2) {
      this.modelState.trendSlope = 0
      return
    }

    // 将时间转换为数值（小时）
    const startTime = data[0].timestamp.getTime()
    const timeScale = 60 * 60 * 1000 // 1小时的毫秒数

    let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0, sumWeights = 0

    // 使用指数衰减权重，最近的数据权重更高
    data.forEach((point, index) => {
      const x = (point.timestamp.getTime() - startTime) / timeScale
      const y = point.doseRate
      const weight = Math.exp(-0.1 * (n - index - 1)) // 指数衰减权重
      
      sumX += x * weight
      sumY += y * weight
      sumXY += x * y * weight
      sumX2 += x * x * weight
      sumWeights += weight
    })

    // 计算加权线性回归系数
    const denominator = sumWeights * sumX2 - sumX * sumX
    if (Math.abs(denominator) > 1e-10) {
      this.modelState.trendSlope = (sumWeights * sumXY - sumX * sumY) / denominator
    } else {
      this.modelState.trendSlope = 0
    }

    console.log('趋势斜率:', this.modelState.trendSlope.toFixed(6))
  }

  /**
   * 提取季节性模式
   */
  extractSeasonalPatterns() {
    const data = this.historicalData
    const patterns = {
      hourly: new Array(24).fill(0),
      daily: new Array(7).fill(0),
      hourlyCounts: new Array(24).fill(0),
      dailyCounts: new Array(7).fill(0)
    }

    // 计算去趋势后的平均值
    const startTime = data[0].timestamp.getTime()
    const timeScale = 60 * 60 * 1000
    const overallMean = data.reduce((sum, item) => sum + item.doseRate, 0) / data.length

    data.forEach(point => {
      const hour = point.timestamp.getHours()
      const dayOfWeek = point.timestamp.getDay()
      
      // 计算去趋势值
      const x = (point.timestamp.getTime() - startTime) / timeScale
      const trendValue = overallMean + this.modelState.trendSlope * x
      const detrended = point.doseRate - trendValue

      // 累积小时和日模式
      patterns.hourly[hour] += detrended
      patterns.daily[dayOfWeek] += detrended
      patterns.hourlyCounts[hour]++
      patterns.dailyCounts[dayOfWeek]++
    })

    // 计算平均模式
    for (let i = 0; i < 24; i++) {
      patterns.hourly[i] = patterns.hourlyCounts[i] > 0 
        ? patterns.hourly[i] / patterns.hourlyCounts[i] 
        : 0
    }

    for (let i = 0; i < 7; i++) {
      patterns.daily[i] = patterns.dailyCounts[i] > 0 
        ? patterns.daily[i] / patterns.dailyCounts[i] 
        : 0
    }

    this.modelState.seasonalPatterns = patterns
    console.log('季节性模式提取完成')
  }

  /**
   * 计算波动性
   */
  calculateVolatility() {
    const data = this.historicalData
    if (data.length < 2) {
      this.modelState.volatility = 0
      return
    }

    // 计算相邻数据点的变化率
    const changes = []
    for (let i = 1; i < data.length; i++) {
      const change = Math.abs(data[i].doseRate - data[i-1].doseRate) / data[i-1].doseRate
      if (!isNaN(change) && isFinite(change)) {
        changes.push(change)
      }
    }

    // 计算标准差作为波动性指标
    if (changes.length > 0) {
      const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length
      const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length
      this.modelState.volatility = Math.sqrt(variance)
    } else {
      this.modelState.volatility = 0.01 // 默认最小波动性
    }

    console.log('波动性:', this.modelState.volatility.toFixed(4))
  }

  /**
   * 生成预测数据（优化版 - 减少数据点数一半）
   * @param {number} predictionHours - 预测小时数，默认84小时（3.5天）
   * @returns {Object} 包含预测数据和误差带的对象
   */
  generatePrediction(predictionHours = 84) {
    if (!this.modelState.isInitialized) {
      throw new Error('模型尚未初始化，请先调用 initializeModel()')
    }

    const lastDataPoint = this.historicalData[this.historicalData.length - 1]
    const startTime = lastDataPoint.timestamp.getTime()
    const timeScale = 2 * 60 * 60 * 1000 // 2小时毫秒数（减少一半数据点）

    const predictionData = []
    const upperBoundData = []
    const lowerBoundData = []

    // 计算基准值
    const baseValue = this.calculateBaseValue()

    // 减少循环次数，每2小时一个数据点
    for (let step = 1; step <= predictionHours; step++) {
      const predictionTime = new Date(startTime + step * timeScale)
      const actualHour = step * 2 // 实际小时数
      const predictedValue = this.predictSinglePoint(predictionTime, baseValue, actualHour)

      // 计算误差带
      const errorBand = this.calculateErrorBand(predictedValue, actualHour)

      predictionData.push({
        timestamp: predictionTime,
        doseRate: predictedValue,
        cps: Math.round(predictedValue * 1000) // 简单的CPS估算
      })

      upperBoundData.push({
        timestamp: predictionTime,
        doseRate: predictedValue + errorBand
      })

      lowerBoundData.push({
        timestamp: predictionTime,
        doseRate: Math.max(0, predictedValue - errorBand) // 确保不为负值
      })
    }

    return {
      prediction: predictionData,
      upperBound: upperBoundData,
      lowerBound: lowerBoundData,
      confidence: this.calculateConfidence(),
      dataPointInterval: 2 // 数据点间隔（小时）
    }
  }

  /**
   * 计算基准值
   * @returns {number} 基准值
   */
  calculateBaseValue() {
    const recentData = this.historicalData.slice(-24) // 最近24小时
    return recentData.reduce((sum, item) => sum + item.doseRate, 0) / recentData.length
  }

  /**
   * 预测单个时间点的值
   * @param {Date} predictionTime - 预测时间
   * @param {number} baseValue - 基准值
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 预测值
   */
  predictSinglePoint(predictionTime, baseValue, hoursAhead) {
    const hour = predictionTime.getHours()
    const dayOfWeek = predictionTime.getDay()

    // 趋势分量
    const trendComponent = this.modelState.trendSlope * hoursAhead

    // 季节性分量
    const hourlyPattern = this.modelState.seasonalPatterns.hourly[hour] || 0
    const dailyPattern = this.modelState.seasonalPatterns.daily[dayOfWeek] || 0
    const seasonalComponent = (hourlyPattern + dailyPattern) * this.modelParameters.seasonalWeight

    // 随机分量（基于历史波动性）
    const randomComponent = this.generateRandomComponent(hoursAhead)

    // 组合预测
    let prediction = baseValue +
                    trendComponent * this.modelParameters.trendWeight +
                    seasonalComponent +
                    randomComponent * this.modelParameters.randomWeight

    // 应用指数平滑
    if (hoursAhead > 1) {
      const smoothingFactor = Math.exp(-hoursAhead / 24) // 距离越远，平滑程度越高
      prediction = baseValue * (1 - smoothingFactor) + prediction * smoothingFactor
    }

    // 确保预测值在合理范围内
    return Math.max(0.001, prediction)
  }

  /**
   * 生成随机分量
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 随机分量
   */
  generateRandomComponent(hoursAhead) {
    // 使用Box-Muller变换生成正态分布随机数
    const u1 = Math.random()
    const u2 = Math.random()
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2)

    // 随机性随时间增加
    const timeDecay = Math.sqrt(hoursAhead / 168) // 7天后达到最大不确定性
    return z0 * this.modelState.volatility * timeDecay
  }

  /**
   * 计算误差带
   * @param {number} predictedValue - 预测值
   * @param {number} hoursAhead - 提前小时数
   * @returns {number} 误差带大小
   */
  calculateErrorBand(predictedValue, hoursAhead) {
    // 基础误差（5%）
    const baseError = predictedValue * this.modelParameters.errorMargin

    // 时间衰减因子（时间越远，误差越大）
    const timeDecayFactor = 1 + (hoursAhead / 168) * 2 // 7天后误差增加到3倍

    // 波动性因子
    const volatilityFactor = 1 + this.modelState.volatility * 10

    return baseError * timeDecayFactor * volatilityFactor
  }

  /**
   * 计算预测置信度
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence() {
    const dataQuality = Math.min(this.historicalData.length / 100, 1) // 数据量质量
    const volatilityPenalty = Math.max(0, 1 - this.modelState.volatility * 5) // 波动性惩罚
    const trendStability = Math.abs(this.modelState.trendSlope) < 0.001 ? 0.9 : 0.7 // 趋势稳定性

    return (dataQuality * 0.4 + volatilityPenalty * 0.4 + trendStability * 0.2)
  }

  /**
   * 更新模型（当有新数据时）- 优化版
   * @param {Array} newData - 新的数据点
   * @returns {Object} 更新后的预测结果
   */
  updateModel(newData) {
    if (!newData || newData.length === 0) return null

    // 添加新数据（不进行预处理抽样，保持实时性）
    const processedNewData = newData
      .filter(item => item && typeof item.doseRate === 'number' && !isNaN(item.doseRate))
      .map(item => ({
        timestamp: new Date(item.timestamp),
        doseRate: item.doseRate,
        cps: item.cps || 0
      }))

    this.historicalData = [...this.historicalData, ...processedNewData]

    // 保持数据窗口大小（最多保留500个数据点，减少一半）
    if (this.historicalData.length > 500) {
      this.historicalData = this.historicalData.slice(-500)
    }

    // 重新训练模型
    this.trainModel()
    this.modelState.lastUpdate = new Date()

    console.log('模型已更新，当前数据点数量:', this.historicalData.length)

    // 立即生成新的预测结果
    return this.generatePrediction()
  }

  /**
   * 实时预测更新
   * @param {Object} latestDataPoint - 最新数据点
   * @returns {Object} 实时预测结果
   */
  realtimeUpdate(latestDataPoint) {
    if (!latestDataPoint || !this.modelState.isInitialized) {
      return null
    }

    // 添加最新数据点
    const newPoint = {
      timestamp: new Date(latestDataPoint.timestamp),
      doseRate: latestDataPoint.doseRate,
      cps: latestDataPoint.cps || 0
    }

    this.historicalData.push(newPoint)

    // 保持数据窗口大小
    if (this.historicalData.length > 500) {
      this.historicalData = this.historicalData.slice(-500)
    }

    // 快速更新（不完全重训练，只更新关键参数）
    this.quickUpdate()

    // 生成实时预测
    return this.generatePrediction()
  }

  /**
   * 快速更新模型参数（用于实时更新）
   */
  quickUpdate() {
    // 只更新最近的趋势和波动性
    const recentData = this.historicalData.slice(-50) // 最近50个数据点

    if (recentData.length >= 2) {
      // 快速计算趋势
      const firstPoint = recentData[0]
      const lastPoint = recentData[recentData.length - 1]
      const timeDiff = (lastPoint.timestamp.getTime() - firstPoint.timestamp.getTime()) / (60 * 60 * 1000)

      if (timeDiff > 0) {
        this.modelState.trendSlope = (lastPoint.doseRate - firstPoint.doseRate) / timeDiff
      }

      // 快速更新波动性
      const values = recentData.map(item => item.doseRate)
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      this.modelState.volatility = Math.sqrt(variance) / mean // 相对波动性
    }

    this.modelState.lastUpdate = new Date()
  }

  /**
   * 获取模型状态信息
   * @returns {Object} 模型状态
   */
  getModelInfo() {
    return {
      isInitialized: this.modelState.isInitialized,
      lastUpdate: this.modelState.lastUpdate,
      dataPoints: this.historicalData.length,
      trendSlope: this.modelState.trendSlope,
      volatility: this.modelState.volatility,
      confidence: this.calculateConfidence()
    }
  }
}

/**
 * 创建预测模型实例的工厂函数
 * @returns {RadiationPredictionModel} 预测模型实例
 */
export function createPredictionModel() {
  return new RadiationPredictionModel()
}
